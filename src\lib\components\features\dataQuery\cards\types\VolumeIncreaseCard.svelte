<script lang="ts">
  import { Badge } from '$lib/components/ui/badge';
  import { Card, CardContent } from '$lib/components/ui/card';
  // 本地实现 getParameterValue 函数
  function getParameterValue(event: any, parameterId: string): string | number | boolean | undefined {
    const parameter = event.parameters.find((p: any) => p.parameterId === parameterId);
    return parameter?.value;
  }
  import {
    getDirectionText,
    formatUsdAmount,
    formatPercentage,
    type BaseEventCardProps
  } from '../utils';

  interface Props extends BaseEventCardProps {}

  const {
    event,
    isHighlighted = false,
    onCardClick
  }: Props = $props();

  // 提取交易量增长相关参数 - 只保留必要的基础信息
  const exchangeLabel = getParameterValue(event, 'exchangeLabel') as string;
  const base = getParameterValue(event, 'base') as string;
  const pair = getParameterValue(event, 'pair') as string;
  const priceDirection = getParameterValue(event, 'priceDirection') as number;
  const recentVolumeSumUsd = getParameterValue(event, 'recentVolumeSumUsd') as number;
  const percentAbnormal = getParameterValue(event, 'percentAbnormal') as number;

  // 获取价格变化方向
  const priceChangeDirection = getDirectionText(priceDirection, 'priceDirection');
  const priceChangeBadgeVariant = priceDirection === 1 ? 'default' : 'destructive';

  // 异常程度判断
  const abnormalLevel = percentAbnormal > 1000 ? '极度异常' :
                       percentAbnormal > 500 ? '高度异常' :
                       percentAbnormal > 200 ? '异常' : '轻微异常';

  // 卡片样式
  const cardClasses = `
    transition-all duration-200 w-full
    ${isHighlighted
      ? 'ring-primary border-primary/50 shadow-lg ring-2'
      : 'hover:shadow-md'
    }
    ${onCardClick ? 'cursor-pointer' : ''}
  `;

  // 处理卡片点击
  function handleCardClick() {
    onCardClick?.(event);
  }
</script>

<Card
  class={cardClasses}
  onclick={onCardClick ? handleCardClick : undefined}
  role={onCardClick ? 'button' : undefined}
  tabindex={onCardClick ? 0 : undefined}
>
  <!-- 简化的卡片内容 -->
  <CardContent class="p-3">
    <!-- 主要信息行 -->
    <div class="flex items-center justify-between mb-2">
      <!-- 左侧：币种和交易对 -->
      <div class="flex items-center space-x-2 min-w-0 flex-1">
        <!-- 币种 -->
        <span class="text-foreground font-semibold text-sm">
          {base || '未知'}
        </span>

        <!-- 交易对 -->
        {#if pair}
          <span class="text-muted-foreground text-xs">
            {pair}
          </span>
        {/if}
      </div>

      <!-- 右侧：价格方向 -->
      <Badge variant={priceChangeBadgeVariant} class="text-xs flex-shrink-0">
        {priceChangeDirection}
      </Badge>
    </div>

    <!-- 交易所和关键数据 -->
    <div class="grid grid-cols-2 gap-2 text-xs">
      <!-- 交易所 -->
      <div class="space-y-1">
        <div class="text-muted-foreground">交易所</div>
        <div class="text-foreground font-medium truncate" title={exchangeLabel}>
          {exchangeLabel || '未知'}
        </div>
      </div>

      <!-- 交易量（关键数据） -->
      <div class="space-y-1">
        <div class="text-muted-foreground">交易量</div>
        <div class="text-foreground font-semibold">
          {formatUsdAmount(recentVolumeSumUsd)}
        </div>
      </div>
    </div>

    <!-- 异常程度 -->
    <div class="mt-2 text-xs">
      <div class="text-muted-foreground">异常程度</div>
      <div class="text-orange-600 dark:text-orange-400 font-semibold">
        {abnormalLevel} (+{formatPercentage(percentAbnormal)}%)
      </div>
    </div>
  </CardContent>
</Card>
