<script lang="ts">
  import { Badge } from '$lib/components/ui/badge';
  import { Card, CardContent } from '$lib/components/ui/card';
  // 本地实现 getParameterValue 函数
  function getParameterValue(event: any, parameterId: string): string | number | boolean | undefined {
    const parameter = event.parameters.find((p: any) => p.parameterId === parameterId);
    return parameter?.value;
  }
  import {
    formatFundingRate,
    type BaseEventCardProps
  } from '../utils';

  interface Props extends BaseEventCardProps {}

  const {
    event,
    isHighlighted = false,
    onCardClick
  }: Props = $props();

  // 提取极端资金费率相关参数 - 只保留必要的基础信息
  const base = getParameterValue(event, 'base') as string;
  const pair = getParameterValue(event, 'pair') as string;
  const side = getParameterValue(event, 'side') as number;
  const exchangeLabel = getParameterValue(event, 'exchangeLabel') as string;
  const fundingRate = getParameterValue(event, 'fundingRate') as number;

  // 获取极端类型
  const extremeType = side === 1 ? '正极端' : side === 2 ? '负极端' : '未知';
  const extremeBadgeVariant = side === 1 ? 'default' : 'destructive';

  // 卡片样式
  const cardClasses = `
    transition-all duration-200 w-full
    ${isHighlighted
      ? 'ring-primary border-primary/50 shadow-lg ring-2'
      : 'hover:shadow-md'
    }
    ${onCardClick ? 'cursor-pointer' : ''}
  `;

  // 处理卡片点击
  function handleCardClick() {
    onCardClick?.(event);
  }
</script>

<Card
  class={cardClasses}
  onclick={onCardClick ? handleCardClick : undefined}
  role={onCardClick ? 'button' : undefined}
  tabindex={onCardClick ? 0 : undefined}
>
  <!-- 简化的卡片内容 -->
  <CardContent class="p-3">
    <!-- 主要信息行 -->
    <div class="flex items-center justify-between mb-2">
      <!-- 左侧：币种和交易对 -->
      <div class="flex items-center space-x-2 min-w-0 flex-1">
        <!-- 币种 -->
        <span class="text-foreground font-semibold text-sm">
          {base || '未知'}
        </span>

        <!-- 交易对 -->
        {#if pair}
          <span class="text-muted-foreground text-xs">
            {pair}
          </span>
        {/if}
      </div>

      <!-- 右侧：极端类型 -->
      <Badge variant={extremeBadgeVariant} class="text-xs flex-shrink-0">
        {extremeType}
      </Badge>
    </div>

    <!-- 交易所和关键数据 -->
    <div class="grid grid-cols-2 gap-2 text-xs">
      <!-- 交易所 -->
      <div class="space-y-1">
        <div class="text-muted-foreground">交易所</div>
        <div class="text-foreground font-medium truncate" title={exchangeLabel}>
          {exchangeLabel || '未知'}
        </div>
      </div>

      <!-- 当前费率（关键数据） -->
      <div class="space-y-1">
        <div class="text-muted-foreground">当前费率</div>
        <div class="text-foreground font-semibold">
          {formatFundingRate(fundingRate)}
        </div>
      </div>
    </div>
  </CardContent>
</Card>
