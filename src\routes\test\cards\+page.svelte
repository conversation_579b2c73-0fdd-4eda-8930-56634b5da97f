<script lang="ts">
  import { EventCardFactory, BaseEventCard, LiquidationOrderCard, TwapDetectionCard, ExchangeTransferCard, OrderbookImbalanceCard } from '$lib/components/features/dataQuery/cards';
  import type { MarketEvent } from '$lib/types';

  // 测试数据
  const testEvents: MarketEvent[] = [
    {
      id: "01JV70KA0VK0ESQ52AN5DX4M2Q",
      marketEventType: "LIQUIDATION_ORDER",
      date: 1747213461,
      parameters: [
        { parameterId: "side", value: 1 },
        { parameterId: "exchange", value: 4096 },
        { parameterId: "exchangeLabel", value: "OKX" },
        { parameterId: "base", value: "BTC" },
        { parameterId: "pair", value: "BTC-USDT-SWAP" },
        { parameterId: "priceUsd", value: 45000.50 },
        { parameterId: "amount", value: 1.5 },
        { parameterId: "amountUsd", value: 67500.75 }
      ],
      currency: "01J8QJG2T7RR1E2V0NFXFGF2RR"
    },
    {
      id: "01JVHRTFGK1G87XEK7X8VXY1R0",
      marketEventType: "TWAP_DETECTION",
      date: 1747574209,
      parameters: [
        { parameterId: "twapId", value: "01JVHRTFCZ1779E2PG5GYPHFKW" },
        { parameterId: "amountUsd", value: 4736.32 },
        { parameterId: "threshold", value: 1 },
        { parameterId: "side", value: 2 },
        { parameterId: "market", value: 1 },
        { parameterId: "duration", value: 4 },
        { parameterId: "status", value: "started" },
        { parameterId: "base", value: "ETH" }
      ],
      currency: "01HAF64YPQCN0VFY2D0SRQQ0MD"
    },
    {
      id: "01JSKYXVDBFRBS8BZ4KWMYWJ3C",
      marketEventType: "EXCHANGE_TRANSFER",
      date: 1745500433,
      parameters: [
        { parameterId: "sender", value: "******************************************" },
        { parameterId: "recipient", value: "******************************************" },
        { parameterId: "exchangeLabel", value: "KRAKEN" },
        { parameterId: "base", value: "BEAM" },
        { parameterId: "amount", value: 1002727 },
        { parameterId: "amountUsd", value: 6871.9 },
        { parameterId: "chain", value: "Ethereum" }
      ],
      currency: "01HEPMMG27T0J8Y1RKW74XHXTM"
    },
    {
      id: "01JSKYG1QHVWE26XCVY3RKW8QX",
      marketEventType: "ORDERBOOK_IMBALANCE",
      date: 1745499981,
      parameters: [
        { parameterId: "deltaUsd", value: 5322.74 },
        { parameterId: "side", value: 2 }, // 2 表示卖盘优势，对应附图中的 "more aggregated asks than bids"
        { parameterId: "threshold", value: 0 },
        { parameterId: "variationPercent", value: 41.53 }, // 对应附图中的 41.53%
        { parameterId: "bidsSumUsd", value: 513800 }, // 对应附图中的 $513.80m
        { parameterId: "asksSumUsd", value: 519530 }, // 对应附图中的 $519.53m
        { parameterId: "bidsSum", value: 513800 },
        { parameterId: "asksSum", value: 519530 },
        { parameterId: "nbPairs", value: 5 },
        { parameterId: "exchangeTopBidder", value: 4 },
        { parameterId: "exchangeTopAsker", value: 512 },
        { parameterId: "exchangeLabelTopBidder", value: "BITGET" },
        { parameterId: "exchangeLabelTopAsker", value: "KRAKEN" },
        { parameterId: "range", value: 30 },
        { parameterId: "exchange", value: -1 },
        { parameterId: "priceUsd", value: 0.08130785714285714 },
        { parameterId: "aggregated", value: true },
        { parameterId: "base", value: "STRX" }, // 对应附图中的 STRX
        { parameterId: "datetime", value: new Date(Date.now() - 60 * 60 * 1000).toISOString() } // 1小时前
      ],
      currency: "01JP2G8RNR4JBJP81JS5JNJQ89"
    },
    {
      id: "01JSM0JMSAAST7BHZW7E02FV7V",
      marketEventType: "VOLUME_INCREASE",
      date: 1745502163,
      parameters: [
        { parameterId: "priceUsd", value: 0.07534 },
        { parameterId: "previousPriceUsd", value: 0.07376 },
        { parameterId: "exchange", value: 32 },
        { parameterId: "exchangeLabel", value: "COINBASE" },
        { parameterId: "base", value: "BIGTIME" },
        { parameterId: "pair", value: "BIGTIME/USDC" },
        { parameterId: "threshold", value: 2 },
        { parameterId: "aggregated", value: false },
        { parameterId: "priceDirection", value: 1 },
        { parameterId: "mean", value: 1427.61 },
        { parameterId: "recentVolumeSumUsd", value: 67178.22 },
        { parameterId: "percentAbnormal", value: 672.24 },
        { parameterId: "isSpot", value: 1 },
        { parameterId: "isDerivatives", value: 0 }
      ],
      currency: "01HCVQSJ0DG9QV5YNV9NP69KQ6"
    },
    {
      id: "01JTZ8PF84P6A1WM2585NHWX9B",
      marketEventType: "OPEN_INTEREST_VARIATION",
      date: 1746953518,
      parameters: [
        { parameterId: "base", value: "SWELL" },
        { parameterId: "pair", value: "SWELL/USDT:USDT" },
        { parameterId: "exchange", value: 4 },
        { parameterId: "priceUsd", value: 0.01204 },
        { parameterId: "direction", value: 2 },
        { parameterId: "threshold", value: 0 },
        { parameterId: "timeframe", value: "15m" },
        { parameterId: "aggregated", value: false },
        { parameterId: "oiVariation", value: -4411074 },
        { parameterId: "absVariation", value: 2.73 },
        { parameterId: "exchangeLabel", value: "BITGET FUTURES" },
        { parameterId: "oiVariationUsd", value: -53109.33 },
        { parameterId: "currentOiAmount", value: 157232051 },
        { parameterId: "previousOiAmount", value: 161643125 },
        { parameterId: "variationPercent", value: -2.73 },
        { parameterId: "currentOiValueUsd", value: 1893073.89 }
      ],
      currency: "01JC303X64R02BZP8P6CZD1BCE"
    }
  ];

  let selectedEvent: MarketEvent | null = $state(null);

  function handleCardClick(event: MarketEvent) {
    selectedEvent = selectedEvent?.id === event.id ? null : event;
  }
</script>

<div class="container mx-auto p-6 space-y-8">
  <div class="space-y-2">
    <h1 class="text-3xl font-bold tracking-tight">卡片组件测试</h1>
    <p class="text-muted-foreground">测试新的事件卡片组件系统</p>
  </div>

  <!-- 基础卡片测试 -->
  <section class="space-y-4">
    <h2 class="text-2xl font-semibold">基础卡片组件</h2>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {#each testEvents as event}
        <BaseEventCard
          {event}
          isHighlighted={selectedEvent?.id === event.id}
          size="md"
          variant="default"
          showActions={true}
          onCardClick={handleCardClick}
        />
      {/each}
    </div>
  </section>

  <!-- 工厂卡片测试 -->
  <section class="space-y-4">
    <h2 class="text-2xl font-semibold">工厂卡片组件</h2>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {#each testEvents as event}
        <EventCardFactory
          {event}
          isHighlighted={selectedEvent?.id === event.id}
          size="md"
          variant="default"
          showActions={true}
          onCardClick={handleCardClick}
          useSpecificCard={true}
        />
      {/each}
    </div>
  </section>

  <!-- 清算订单专门测试 -->
  <section class="space-y-4">
    <h2 class="text-2xl font-semibold">清算订单卡片测试</h2>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {#each testEvents.filter(e => e.marketEventType === 'LIQUIDATION_ORDER') as event}
        <LiquidationOrderCard
          {event}
          isHighlighted={selectedEvent?.id === event.id}
          size="md"
          variant="detailed"
          showActions={true}
          onCardClick={handleCardClick}
        />
      {/each}
    </div>
  </section>

  <!-- TWAP检测专门测试 -->
  <section class="space-y-4">
    <h2 class="text-2xl font-semibold">TWAP检测卡片测试</h2>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {#each testEvents.filter(e => e.marketEventType === 'TWAP_DETECTION') as event}
        <TwapDetectionCard
          {event}
          isHighlighted={selectedEvent?.id === event.id}
          size="md"
          variant="detailed"
          showActions={true}
          onCardClick={handleCardClick}
        />
      {/each}
    </div>
  </section>

  <!-- 交易所转账专门测试 -->
  <section class="space-y-4">
    <h2 class="text-2xl font-semibold">交易所转账卡片测试</h2>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {#each testEvents.filter(e => e.marketEventType === 'EXCHANGE_TRANSFER') as event}
        <ExchangeTransferCard
          {event}
          isHighlighted={selectedEvent?.id === event.id}
          size="md"
          variant="detailed"
          showActions={true}
          onCardClick={handleCardClick}
        />
      {/each}
    </div>
  </section>

  <!-- 订单簿失衡专门测试 -->
  <section class="space-y-4">
    <h2 class="text-2xl font-semibold">订单簿失衡卡片测试</h2>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {#each testEvents.filter(e => e.marketEventType === 'ORDERBOOK_IMBALANCE') as event}
        <OrderbookImbalanceCard
          {event}
          isHighlighted={selectedEvent?.id === event.id}
          size="md"
          variant="detailed"
          showActions={true}
          onCardClick={handleCardClick}
        />
      {/each}
    </div>
  </section>

  <!-- 不同尺寸测试 -->
  <section class="space-y-4">
    <h2 class="text-2xl font-semibold">不同尺寸测试</h2>
    <div class="space-y-4">
      <h3 class="text-lg font-medium">小尺寸 (sm)</h3>
      <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-3">
        {#each testEvents as event}
          <EventCardFactory
            {event}
            size="sm"
            variant="compact"
            onCardClick={handleCardClick}
          />
        {/each}
      </div>

      <h3 class="text-lg font-medium">大尺寸 (lg)</h3>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        {#each testEvents as event}
          <EventCardFactory
            {event}
            size="lg"
            variant="detailed"
            showActions={true}
            onCardClick={handleCardClick}
          />
        {/each}
      </div>
    </div>
  </section>

  <!-- 选中事件详情 -->
  {#if selectedEvent}
    <section class="space-y-4">
      <h2 class="text-2xl font-semibold">选中事件详情</h2>
      <div class="bg-muted/50 rounded-lg p-4">
        <pre class="text-sm overflow-auto">{JSON.stringify(selectedEvent, null, 2)}</pre>
      </div>
    </section>
  {/if}
</div>
